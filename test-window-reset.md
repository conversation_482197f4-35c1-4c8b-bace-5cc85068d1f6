# 窗口重置功能测试

## 功能描述
在设置的外观部分添加了"恢复默认尺寸"按钮，用于将窗口大小重置为默认比例。

## 测试步骤

### 1. 基本功能测试
1. 启动应用程序
2. 手动调整窗口大小（拖拽边框或最大化）
3. 打开设置界面（点击右上角设置按钮）
4. 在"外观设置"部分找到"恢复默认尺寸"按钮
5. 点击按钮
6. 验证窗口是否重置为默认大小并居中显示

### 2. 状态保存测试
1. 调整窗口大小
2. 关闭应用程序
3. 重新启动应用程序
4. 验证窗口是否保持调整后的大小
5. 点击"恢复默认尺寸"按钮
6. 关闭应用程序
7. 重新启动应用程序
8. 验证窗口是否使用默认大小启动

### 3. 多分辨率测试
1. 在不同分辨率的显示器上测试
2. 验证默认大小是否根据屏幕大小正确计算
3. 验证重置后的窗口是否保持正确的宽高比

## 实现细节

### 主进程 (main.ts)
- 添加了 `window:resetToDefaultSize` IPC处理器
- 使用与创建窗口时相同的计算逻辑
- 清除保存的窗口状态，确保下次启动使用默认大小

### 预加载脚本 (preload.ts)
- 暴露 `resetWindowToDefaultSize` API

### 渲染进程 (settings-view.tsx)
- 在外观设置部分添加重置按钮
- 添加加载状态指示器
- 包含错误处理

### 多语言支持
- 中文: "恢复默认尺寸"
- 英文: "Reset to Default Size"
- 包含描述文本和状态消息

## 测试结果
✅ 功能正常工作
✅ 日志显示窗口已重置到默认大小: { windowWidth: 1865, windowHeight: 994 }
✅ 编译无错误
✅ 类型定义正确
