"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryManager = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const electron_1 = require("electron");
const workflow_engine_1 = require("./workflow-engine");
// 历史记录文件路径
const HISTORY_FILE_PATH = path_1.default.join(electron_1.app.getPath('userData'), 'history.json');
// 历史记录配置
const HISTORY_CONFIG = {
    MAX_ENTRIES: 1000, // 最大历史记录数量
    AUTO_CLEANUP_DAYS: 30, // 自动清理超过30天的记录
    MEMORY_CLEANUP_THRESHOLD: 500 // 内存中超过500条时触发清理
};
// 读取历史记录
async function loadHistory() {
    try {
        if (await fs_extra_1.default.pathExists(HISTORY_FILE_PATH)) {
            const data = await fs_extra_1.default.readFile(HISTORY_FILE_PATH, 'utf-8');
            return JSON.parse(data);
        }
        return [];
    }
    catch (error) {
        console.error('Failed to load history:', error);
        return [];
    }
}
// 保存历史记录
async function saveHistory(history) {
    try {
        await fs_extra_1.default.ensureDir(path_1.default.dirname(HISTORY_FILE_PATH));
        await fs_extra_1.default.writeFile(HISTORY_FILE_PATH, JSON.stringify(history, null, 2), 'utf-8');
    }
    catch (error) {
        console.error('Failed to save history:', error);
        throw error;
    }
}
class HistoryManager {
    constructor() {
        this.memoryCache = null;
        this.lastCleanupTime = 0;
        this.operationLocks = new Map(); // 操作锁
        this.isHistoryLocked = false; // 历史记录文件锁
        this.operationLog = new Map(); // 操作日志，用于回滚
        this.workflowEngine = new workflow_engine_1.WorkflowEngine();
    }
    /**
     * 获取操作锁，防止并发操作冲突
     */
    async acquireOperationLock(entryId) {
        const existingLock = this.operationLocks.get(entryId);
        if (existingLock) {
            await existingLock; // 等待现有操作完成
        }
    }
    /**
     * 释放操作锁
     */
    releaseOperationLock(entryId) {
        this.operationLocks.delete(entryId);
    }
    /**
     * 获取历史记录文件锁
     */
    async acquireHistoryLock() {
        while (this.isHistoryLocked) {
            await new Promise(resolve => setTimeout(resolve, 50)); // 等待50ms后重试
        }
        this.isHistoryLocked = true;
    }
    /**
     * 释放历史记录文件锁
     */
    releaseHistoryLock() {
        this.isHistoryLocked = false;
    }
    /**
     * 记录操作步骤，用于回滚
     */
    logOperationStep(operationId, step) {
        if (!this.operationLog.has(operationId)) {
            this.operationLog.set(operationId, []);
        }
        this.operationLog.get(operationId).push(step);
    }
    /**
     * 回滚操作步骤
     */
    async rollbackOperation(operationId) {
        const steps = this.operationLog.get(operationId);
        if (!steps)
            return;
        console.log(`🔄 开始回滚操作 ${operationId}，共 ${steps.length} 个步骤`);
        // 按相反顺序回滚已完成的步骤
        for (let i = steps.length - 1; i >= 0; i--) {
            const step = steps[i];
            if (!step.completed)
                continue;
            try {
                await this.rollbackSingleStep(step);
                console.log(`✅ 回滚步骤成功: ${step.type} - ${step.id}`);
            }
            catch (error) {
                console.error(`❌ 回滚步骤失败: ${step.type} - ${step.id}`, error);
                // 继续回滚其他步骤，不要因为一个失败就停止
            }
        }
        // 清理操作日志
        this.operationLog.delete(operationId);
    }
    /**
     * 回滚单个步骤
     */
    async rollbackSingleStep(step) {
        switch (step.type) {
            case 'file_move':
                if (step.targetPath && step.sourcePath && await fs_extra_1.default.pathExists(step.targetPath)) {
                    await fs_extra_1.default.move(step.targetPath, step.sourcePath);
                }
                break;
            case 'file_copy':
                if (step.targetPath && await fs_extra_1.default.pathExists(step.targetPath)) {
                    await fs_extra_1.default.remove(step.targetPath);
                }
                break;
            case 'file_delete':
                if (step.backupPath && step.sourcePath && await fs_extra_1.default.pathExists(step.backupPath)) {
                    await fs_extra_1.default.move(step.backupPath, step.sourcePath);
                }
                break;
            case 'folder_create':
                if (step.targetPath && await fs_extra_1.default.pathExists(step.targetPath)) {
                    const items = await fs_extra_1.default.readdir(step.targetPath);
                    if (items.length === 0) {
                        await fs_extra_1.default.rmdir(step.targetPath);
                    }
                }
                break;
            case 'history_update':
                // 历史记录的回滚需要特殊处理
                if (step.metadata && step.metadata.originalEntry) {
                    await this.restoreHistoryEntry(step.metadata.originalEntry);
                }
                break;
        }
    }
    /**
     * 恢复历史记录条目
     */
    async restoreHistoryEntry(originalEntry) {
        await this.acquireHistoryLock();
        try {
            const history = await loadHistory();
            const entryIndex = history.findIndex(entry => entry.id === originalEntry.id);
            if (entryIndex !== -1) {
                history[entryIndex] = originalEntry;
                await saveHistory(history);
                this.clearMemoryCache();
            }
        }
        finally {
            this.releaseHistoryLock();
        }
    }
    /**
     * 验证路径安全性，防止路径遍历攻击
     */
    validatePathSecurity(filePath, basePath) {
        try {
            // 规范化路径
            const normalizedPath = require('path').resolve(filePath);
            // 检查路径是否包含危险字符
            if (filePath.includes('..') || filePath.includes('~')) {
                return { isValid: false, error: '路径包含不安全字符' };
            }
            // 如果提供了基础路径，检查是否在允许范围内
            if (basePath) {
                const normalizedBasePath = require('path').resolve(basePath);
                if (!normalizedPath.startsWith(normalizedBasePath)) {
                    return { isValid: false, error: '路径超出允许范围' };
                }
            }
            // 检查路径长度（Windows 路径限制）
            if (normalizedPath.length > 260) {
                return { isValid: false, error: '路径过长' };
            }
            // 检查是否为系统关键目录
            const systemPaths = [
                'C:\\Windows',
                'C:\\Program Files',
                'C:\\Program Files (x86)',
                '/System',
                '/usr/bin',
                '/bin',
                '/sbin'
            ];
            for (const systemPath of systemPaths) {
                if (normalizedPath.toLowerCase().startsWith(systemPath.toLowerCase())) {
                    return { isValid: false, error: '不能操作系统关键目录' };
                }
            }
            return { isValid: true };
        }
        catch (error) {
            return { isValid: false, error: `路径验证失败: ${error instanceof Error ? error.message : String(error)}` };
        }
    }
    /**
     * 规范化文件操作路径
     */
    normalizeOperationPaths(operation) {
        try {
            const normalizedOperation = { ...operation };
            // 验证原始路径
            if (operation.originalPath) {
                const validation = this.validatePathSecurity(operation.originalPath);
                if (!validation.isValid) {
                    return { isValid: false, error: `原始路径不安全: ${validation.error}` };
                }
                normalizedOperation.originalPath = require('path').resolve(operation.originalPath);
            }
            // 验证新路径
            if (operation.newPath) {
                const validation = this.validatePathSecurity(operation.newPath);
                if (!validation.isValid) {
                    return { isValid: false, error: `目标路径不安全: ${validation.error}` };
                }
                normalizedOperation.newPath = require('path').resolve(operation.newPath);
            }
            return { isValid: true, normalizedOperation };
        }
        catch (error) {
            return { isValid: false, error: `路径规范化失败: ${error instanceof Error ? error.message : String(error)}` };
        }
    }
    /**
     * 添加历史记录条目
     */
    async addEntry(entry) {
        await this.acquireHistoryLock();
        try {
            const history = await loadHistory();
            history.unshift(entry); // 添加到开头，最新的在前面
            // 执行清理策略
            const cleanedHistory = this.performCleanup(history);
            await saveHistory(cleanedHistory);
            // 清除内存缓存，强制下次重新加载
            this.memoryCache = null;
        }
        finally {
            this.releaseHistoryLock();
        }
    }
    /**
     * 执行历史记录清理
     */
    performCleanup(history) {
        const now = Date.now();
        // 1. 按数量限制清理
        if (history.length > HISTORY_CONFIG.MAX_ENTRIES) {
            history = history.slice(0, HISTORY_CONFIG.MAX_ENTRIES);
            console.log(`[历史记录] 按数量限制清理，保留最近${HISTORY_CONFIG.MAX_ENTRIES}条记录`);
        }
        // 2. 按时间清理（每小时最多执行一次）
        if (now - this.lastCleanupTime > 60 * 60 * 1000) {
            const cutoffTime = now - (HISTORY_CONFIG.AUTO_CLEANUP_DAYS * 24 * 60 * 60 * 1000);
            const beforeCount = history.length;
            history = history.filter(entry => {
                const entryTime = new Date(entry.timestamp).getTime();
                return entryTime > cutoffTime;
            });
            if (beforeCount !== history.length) {
                console.log(`[历史记录] 按时间清理，删除了${beforeCount - history.length}条超过${HISTORY_CONFIG.AUTO_CLEANUP_DAYS}天的记录`);
            }
            this.lastCleanupTime = now;
        }
        return history;
    }
    /**
     * 从工作流结果创建历史记录条目
     */
    createEntryFromWorkflowResult(workflowResult, workflow, originalFiles, source = 'manual', monitorTaskId, monitorTaskName, createdDirectories, cleanedEmptyDirectories) {
        const fileOperations = [];
        // 遍历每个步骤的结果来构建文件操作记录
        for (const stepResult of workflowResult.stepResults) {
            for (let i = 0; i < stepResult.outputFiles.length; i++) {
                const outputFile = stepResult.outputFiles[i];
                const inputFile = stepResult.inputFiles[i];
                if (inputFile && outputFile) {
                    // 确定操作类型
                    let operation = 'move';
                    if (outputFile.path === '将被删除') {
                        operation = 'delete';
                    }
                    else if (inputFile.path !== outputFile.path) {
                        // 检查是否只是重命名还是移动
                        const inputDir = require('path').dirname(inputFile.path);
                        const outputDir = require('path').dirname(outputFile.path);
                        if (inputDir === outputDir) {
                            operation = 'rename';
                        }
                        else {
                            operation = 'move';
                        }
                    }
                    // 对于所有操作，originalPath都应该是完整的原始路径
                    // 撤销时会将文件/文件夹移回到这个完整路径
                    let originalPath = inputFile.path;
                    console.log(`📝 操作记录: ${inputFile.path} -> ${outputFile.path} (${operation})`);
                    console.log(`📝 撤销时将回到: ${originalPath}`);
                    const fileOperation = {
                        id: `op-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                        originalPath: originalPath,
                        originalName: inputFile.name,
                        newPath: outputFile.path !== '将被删除' ? outputFile.path : undefined,
                        newName: outputFile.path !== '将被删除' ? outputFile.name : undefined,
                        operation,
                        status: outputFile.status === 'error' ? 'error' : 'success',
                        error: outputFile.error,
                        fileType: inputFile.type,
                        fileSize: inputFile.size
                    };
                    fileOperations.push(fileOperation);
                }
            }
        }
        // 添加被清理的空文件夹到文件操作记录中
        const allFileOperations = [...fileOperations];
        if (cleanedEmptyDirectories && cleanedEmptyDirectories.length > 0) {
            const emptyFolderOperations = cleanedEmptyDirectories.map(dirPath => ({
                id: `empty-folder-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                originalPath: dirPath,
                originalName: path_1.default.basename(dirPath),
                newPath: undefined, // 被清理，没有新路径
                newName: undefined,
                operation: 'cleanup_empty_folder', // 特殊操作类型
                status: 'success',
                timestamp: workflowResult.startTime,
                type: 'folder',
                size: 0,
                fileType: 'folder', // 添加缺少的字段
                fileSize: 0, // 添加缺少的字段
                error: undefined
            }));
            allFileOperations.push(...emptyFolderOperations);
        }
        // 检查是否包含删除操作
        const hasDeleteOperation = fileOperations.some(op => op.operation === 'delete');
        // 检查是否有成功的文件操作
        const hasSuccessfulOperations = fileOperations.some(op => op.status === 'success');
        // 创建历史记录条目
        const historyEntry = {
            id: `history-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            timestamp: workflowResult.startTime,
            workflowId: workflow.id,
            workflowName: workflow.name,
            fileOperations: allFileOperations,
            status: workflowResult.errors.length === 0 ? 'success' :
                workflowResult.errors.length === workflowResult.totalFiles ? 'error' : 'partial',
            duration: workflowResult.duration,
            totalFiles: workflowResult.totalFiles,
            processedFiles: workflowResult.processedFiles,
            errors: workflowResult.errors,
            // 撤销相关字段 - 只要有成功的操作且不包含删除操作就可以撤销
            canUndo: hasSuccessfulOperations && !hasDeleteOperation,
            isUndone: false,
            // 工作流执行过程中创建的文件夹
            createdDirectories: createdDirectories || [],
            // 被清理的空文件夹（用于撤销时恢复）
            cleanedEmptyDirectories: cleanedEmptyDirectories || [],
            // 监控来源相关字段
            source,
            monitorTaskId,
            monitorTaskName
        };
        return historyEntry;
    }
    /**
     * 获取历史记录（带内存缓存优化）
     */
    async getEntries(limit, offset) {
        // 使用内存缓存减少文件读取
        if (!this.memoryCache) {
            this.memoryCache = await loadHistory();
            // 如果内存中的记录过多，触发清理
            if (this.memoryCache.length > HISTORY_CONFIG.MEMORY_CLEANUP_THRESHOLD) {
                this.memoryCache = this.performCleanup(this.memoryCache);
                await saveHistory(this.memoryCache);
            }
        }
        if (limit !== undefined && offset !== undefined) {
            return this.memoryCache.slice(offset, offset + limit);
        }
        return this.memoryCache;
    }
    /**
     * 清除内存缓存
     */
    clearMemoryCache() {
        this.memoryCache = null;
    }
    /**
     * 搜索历史记录（使用内存缓存）
     */
    async searchEntries(query, limit) {
        const history = await this.getEntries(); // 使用缓存的获取方法
        const lowerQuery = query.toLowerCase();
        const filtered = history.filter((entry) => entry.workflowName.toLowerCase().includes(lowerQuery) ||
            entry.stepName?.toLowerCase().includes(lowerQuery) ||
            entry.monitorTaskName?.toLowerCase().includes(lowerQuery) ||
            entry.fileOperations.some((op) => op.originalName.toLowerCase().includes(lowerQuery) ||
                op.newName?.toLowerCase().includes(lowerQuery)));
        return limit ? filtered.slice(0, limit) : filtered;
    }
    /**
     * 清空历史记录
     */
    async clearHistory() {
        await saveHistory([]);
        this.memoryCache = [];
    }
    /**
     * 删除单条历史记录
     */
    async deleteEntry(entryId) {
        const history = await this.getEntries(); // 使用缓存
        const filteredHistory = history.filter((entry) => entry.id !== entryId);
        if (filteredHistory.length !== history.length) {
            await saveHistory(filteredHistory);
            this.memoryCache = filteredHistory; // 更新缓存
            return true;
        }
        return false;
    }
    /**
     * 检查文件/文件夹权限（复用工作流引擎的权限检查）
     */
    async checkPermissions(filePath, operation) {
        try {
            const hasPermission = await this.workflowEngine.checkPermissions(filePath, operation);
            if (hasPermission) {
                return { hasPermission: true };
            }
            else {
                return {
                    hasPermission: false,
                    error: `${operation === 'read' ? '读取' : '写入'}权限不足`
                };
            }
        }
        catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            return {
                hasPermission: false,
                error: `权限检查失败: ${errorMsg}`
            };
        }
    }
    /**
     * 检查目录权限（包括父目录）
     */
    async checkDirectoryPermissions(dirPath) {
        try {
            // 检查目录是否存在
            if (await fs_extra_1.default.pathExists(dirPath)) {
                // 目录存在，检查写入权限
                return await this.checkPermissions(dirPath, 'write');
            }
            else {
                // 目录不存在，检查父目录的写入权限
                const parentDir = require('path').dirname(dirPath);
                if (parentDir === dirPath) {
                    // 已经到根目录
                    return { hasPermission: false, error: '无法访问根目录' };
                }
                return await this.checkDirectoryPermissions(parentDir);
            }
        }
        catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            return { hasPermission: false, error: `目录权限检查失败: ${errorMsg}` };
        }
    }
    /**
     * 检查磁盘空间是否足够（复用工作流引擎的磁盘空间检查）
     */
    async checkDiskSpace(targetPath, requiredSize) {
        return await this.workflowEngine.checkDiskSpace(targetPath, requiredSize);
    }
    /**
     * 计算撤回操作所需的磁盘空间
     */
    calculateRequiredSpace(operations) {
        let totalSize = 0;
        for (const operation of operations) {
            if (operation.status === 'success') {
                // 对于移动和重命名操作，通常不需要额外空间
                // 对于复制操作的撤回（删除），会释放空间
                // 这里主要考虑可能的临时文件空间需求
                if (operation.operation === 'move' || operation.operation === 'rename') {
                    // 移动操作可能需要临时空间（如果跨分区）
                    totalSize += operation.fileSize;
                }
            }
        }
        return totalSize;
    }
    /**
     * 分类和格式化错误信息（复用工作流引擎的错误分类）
     */
    categorizeError(error, operation, filePath) {
        return this.workflowEngine.categorizeError(error, operation, filePath);
    }
    /**
     * 生成用户友好的错误建议
     */
    generateErrorSuggestion(error) {
        if (error.includes('权限不足')) {
            return '建议：请以管理员身份运行程序，或检查文件/文件夹权限设置';
        }
        else if (error.includes('文件被占用')) {
            return '建议：请关闭正在使用该文件的程序，然后重试';
        }
        else if (error.includes('磁盘空间不足')) {
            return '建议：请清理磁盘空间或选择其他位置';
        }
        else if (error.includes('文件不存在')) {
            return '建议：文件可能已被手动删除或移动，请检查文件位置';
        }
        else if (error.includes('目标已存在')) {
            return '建议：请检查目标位置是否有同名文件，考虑重命名或删除冲突文件';
        }
        else {
            return '建议：请检查文件状态和系统环境，必要时手动恢复文件';
        }
    }
    /**
     * 事务性更新历史记录条目状态
     */
    async updateHistoryEntryStatus(entryId, updates) {
        try {
            // 重新加载最新的历史记录，避免并发修改冲突
            const history = await loadHistory();
            const entryIndex = history.findIndex((entry) => entry.id === entryId);
            if (entryIndex === -1) {
                throw new Error(`历史记录条目不存在: ${entryId}`);
            }
            // 更新条目状态
            history[entryIndex] = {
                ...history[entryIndex],
                ...updates
            };
            // 原子性保存
            await saveHistory(history);
            // 清除内存缓存，确保前端获取到最新状态
            this.clearMemoryCache();
            console.log(`✅ 历史记录状态已更新: ${entryId}`, updates);
        }
        catch (error) {
            console.error(`❌ 更新历史记录状态失败: ${entryId}`, error);
            throw new Error(`更新历史记录状态失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * 手动触发清理
     */
    async manualCleanup() {
        const history = await loadHistory();
        const beforeCount = history.length;
        const cleanedHistory = this.performCleanup(history);
        const deletedCount = beforeCount - cleanedHistory.length;
        if (deletedCount > 0) {
            await saveHistory(cleanedHistory);
            this.memoryCache = cleanedHistory;
            return {
                deletedCount,
                message: `已清理${deletedCount}条历史记录`
            };
        }
        return {
            deletedCount: 0,
            message: '无需清理'
        };
    }
    /**
     * 撤销历史记录操作
     */
    async undoEntry(entryId) {
        // 获取操作锁，防止并发撤回同一条记录
        await this.acquireOperationLock(entryId);
        const operationPromise = this.performUndoEntry(entryId);
        this.operationLocks.set(entryId, operationPromise);
        try {
            const result = await operationPromise;
            return result;
        }
        finally {
            this.releaseOperationLock(entryId);
        }
    }
    /**
     * 执行撤销操作的核心逻辑
     */
    async performUndoEntry(entryId) {
        try {
            console.log('开始撤销操作，entryId:', entryId);
            await this.acquireHistoryLock();
            let history;
            try {
                history = await loadHistory();
                console.log('当前历史记录数量:', history.length);
            }
            finally {
                this.releaseHistoryLock();
            }
            const entryIndex = history.findIndex((entry) => entry.id === entryId);
            if (entryIndex === -1) {
                return { success: false, message: '历史记录不存在，可能已被删除' };
            }
            const entry = history[entryIndex];
            console.log('找到要撤销的记录:', entry.workflowName);
            // 检查是否可以撤销（兼容旧数据，canUndo为undefined时默认可以撤销）
            if (entry.canUndo === false) {
                return { success: false, message: '此操作被标记为不可撤销' };
            }
            if (entry.isUndone) {
                return { success: false, message: '此操作已经被撤销过了' };
            }
            // 检查撤销时间限制（24小时）
            const entryTime = new Date(entry.timestamp).getTime();
            const now = Date.now();
            const hoursDiff = (now - entryTime) / (1000 * 60 * 60);
            if (hoursDiff > 24) {
                const hoursAgo = Math.floor(hoursDiff);
                return {
                    success: false,
                    message: `操作已过去 ${hoursAgo} 小时，超过24小时时间限制，无法撤销。请手动恢复文件。`
                };
            }
            // 预检查撤销操作的可行性
            const preCheckResult = await this.preCheckUndoOperations(entry.fileOperations);
            if (!preCheckResult.canUndo) {
                // 检查是否是连锁重命名冲突
                const hasChainConflict = preCheckResult.issues.some(issue => issue.includes('[连锁冲突]'));
                if (hasChainConflict) {
                    // 提供连锁撤回选项
                    return {
                        success: false,
                        message: `检测到连锁重命名冲突:\n${preCheckResult.issues.join('\n')}\n\n解决方案：\n1. 使用连锁撤回功能自动处理依赖关系\n2. 手动逐个撤回相关操作\n3. 手动恢复文件位置`,
                        requiresChainUndo: true,
                        entryId: entryId
                    };
                }
                else {
                    // 分析错误类型并提供针对性建议
                    const hasPermissionIssues = preCheckResult.issues.some(issue => issue.includes('权限不足'));
                    const hasSpaceIssues = preCheckResult.issues.some(issue => issue.includes('磁盘空间'));
                    const hasFileIssues = preCheckResult.issues.some(issue => issue.includes('不存在') || issue.includes('已被占用'));
                    let suggestion = '';
                    if (hasPermissionIssues) {
                        suggestion = '\n💡 解决方案：请以管理员身份运行程序，或检查文件权限设置';
                    }
                    else if (hasSpaceIssues) {
                        suggestion = '\n💡 解决方案：请清理磁盘空间或等待其他操作完成';
                    }
                    else if (hasFileIssues) {
                        suggestion = '\n💡 解决方案：请检查文件是否被手动移动或删除，考虑手动恢复';
                    }
                    else {
                        suggestion = '\n💡 解决方案：请检查系统状态，必要时手动恢复文件';
                    }
                    return {
                        success: false,
                        message: `撤销预检查失败:\n${preCheckResult.issues.join('\n')}${suggestion}`
                    };
                }
            }
            // 执行撤销操作 - 使用事务性处理确保数据一致性
            console.log('开始执行文件撤销操作');
            const operationId = `undo-${entryId}-${Date.now()}`;
            try {
                // 预验证所有操作的路径安全性
                for (const operation of entry.fileOperations) {
                    if (operation.status !== 'success')
                        continue;
                    const pathValidation = this.normalizeOperationPaths(operation);
                    if (!pathValidation.isValid) {
                        throw new Error(`路径安全验证失败: ${pathValidation.error}`);
                    }
                }
                await this.performUndoOperations(entry.fileOperations, operationId);
                console.log('文件撤销操作完成');
                // 清理工作流执行过程中创建的文件夹
                console.log('🔍 检查文件夹清理条件:', {
                    hasCreatedDirectories: !!entry.createdDirectories,
                    createdDirectoriesLength: entry.createdDirectories?.length || 0,
                    createdDirectories: entry.createdDirectories
                });
                if (entry.createdDirectories && entry.createdDirectories.length > 0) {
                    console.log('开始清理工作流创建的文件夹');
                    await this.cleanupCreatedDirectories(entry.createdDirectories);
                    console.log('文件夹清理完成');
                }
                else {
                    console.log('⚠️ 没有需要清理的文件夹或文件夹列表为空');
                }
                // 恢复被清理的空文件夹
                console.log('🔍 检查空文件夹恢复条件:', {
                    hasCleanedEmptyDirectories: !!entry.cleanedEmptyDirectories,
                    cleanedEmptyDirectoriesLength: entry.cleanedEmptyDirectories?.length || 0,
                    cleanedEmptyDirectories: entry.cleanedEmptyDirectories
                });
                if (entry.cleanedEmptyDirectories && entry.cleanedEmptyDirectories.length > 0) {
                    console.log('开始恢复被清理的空文件夹');
                    await this.restoreCleanedEmptyDirectories(entry.cleanedEmptyDirectories);
                    console.log('空文件夹恢复完成');
                }
                else {
                    console.log('⚠️ 没有需要恢复的空文件夹或文件夹列表为空');
                }
                // 记录历史记录更新前的状态，用于回滚
                if (operationId) {
                    this.logOperationStep(operationId, {
                        id: `history-update-${Date.now()}`,
                        type: 'history_update',
                        metadata: { originalEntry: { ...entry } },
                        timestamp: Date.now(),
                        completed: false
                    });
                }
                // 事务性更新历史记录状态
                await this.updateHistoryEntryStatus(entryId, {
                    isUndone: true,
                    undoTimestamp: new Date().toISOString(),
                    canUndo: false
                });
                // 标记历史记录更新完成
                if (operationId) {
                    const steps = this.operationLog.get(operationId);
                    if (steps) {
                        const lastStep = steps[steps.length - 1];
                        if (lastStep && lastStep.type === 'history_update') {
                            lastStep.completed = true;
                        }
                    }
                }
                console.log('撤销操作完成，历史记录已更新');
                // 清理操作日志
                if (operationId) {
                    this.operationLog.delete(operationId);
                }
            }
            catch (undoError) {
                // 撤销操作失败，执行回滚
                console.error('撤销操作失败，开始回滚操作');
                if (operationId) {
                    try {
                        await this.rollbackOperation(operationId);
                        console.log('回滚操作完成');
                    }
                    catch (rollbackError) {
                        console.error('回滚操作也失败了:', rollbackError);
                    }
                }
                throw undoError;
            }
            return { success: true, message: '撤销操作成功完成' };
        }
        catch (error) {
            console.error('撤销操作失败:', error);
            // 提供更详细的错误信息
            let errorMessage = '撤销操作失败';
            if (error instanceof Error) {
                if (error.message.includes('ENOENT')) {
                    errorMessage = '撤销失败：相关文件或文件夹不存在，可能已被手动删除或移动';
                }
                else if (error.message.includes('EACCES') || error.message.includes('EPERM')) {
                    errorMessage = '撤销失败：权限不足，请以管理员身份运行或检查文件权限';
                }
                else if (error.message.includes('EBUSY')) {
                    errorMessage = '撤销失败：文件正在被其他程序使用，请关闭相关程序后重试';
                }
                else if (error.message.includes('EEXIST')) {
                    errorMessage = '撤销失败：目标位置已存在同名文件或文件夹';
                }
                else {
                    errorMessage = `撤销失败：${error.message}`;
                }
            }
            return { success: false, message: errorMessage };
        }
    }
    /**
     * 连锁撤回操作 - 处理连锁重命名冲突
     */
    async chainUndoEntry(entryId) {
        try {
            console.log('开始连锁撤回操作，entryId:', entryId);
            const history = await loadHistory();
            const entryIndex = history.findIndex((entry) => entry.id === entryId);
            if (entryIndex === -1) {
                return { success: false, message: '历史记录不存在，可能已被删除' };
            }
            const entry = history[entryIndex];
            console.log('找到要连锁撤回的记录:', entry.workflowName);
            // 检查基本撤回条件
            if (entry.canUndo === false) {
                return { success: false, message: '此操作被标记为不可撤销' };
            }
            if (entry.isUndone) {
                return { success: false, message: '此操作已经被撤销过了' };
            }
            // 检查时间限制
            const entryTime = new Date(entry.timestamp).getTime();
            const now = Date.now();
            const hoursDiff = (now - entryTime) / (1000 * 60 * 60);
            if (hoursDiff > 24) {
                const hoursAgo = Math.floor(hoursDiff);
                return {
                    success: false,
                    message: `操作已过去 ${hoursAgo} 小时，超过24小时时间限制，无法撤销。`
                };
            }
            // 分析连锁依赖关系
            const chainAnalysis = await this.analyzeChainDependencies(entry.fileOperations);
            console.log('连锁依赖分析结果:', chainAnalysis);
            if (chainAnalysis.conflicts.length === 0) {
                // 没有连锁冲突，执行普通撤回操作（避免递归调用）
                console.log('开始执行普通撤回操作');
                await this.performUndoOperations(entry.fileOperations);
                console.log('普通撤回操作完成');
                // 清理工作流创建的文件夹
                if (entry.createdDirectories && entry.createdDirectories.length > 0) {
                    console.log('开始清理工作流创建的文件夹');
                    await this.cleanupCreatedDirectories(entry.createdDirectories);
                    console.log('文件夹清理完成');
                }
                // 恢复被清理的空文件夹
                if (entry.cleanedEmptyDirectories && entry.cleanedEmptyDirectories.length > 0) {
                    console.log('开始恢复被清理的空文件夹');
                    await this.restoreCleanedEmptyDirectories(entry.cleanedEmptyDirectories);
                    console.log('空文件夹恢复完成');
                }
                // 更新历史记录状态
                const history = await loadHistory();
                const entryIndex = history.findIndex((e) => e.id === entryId);
                if (entryIndex !== -1) {
                    history[entryIndex] = {
                        ...entry,
                        isUndone: true,
                        undoTimestamp: new Date().toISOString(),
                        canUndo: false
                    };
                    await saveHistory(history);
                    this.clearMemoryCache();
                }
                return { success: true, message: '撤回操作成功完成' };
            }
            // 执行连锁撤回
            console.log('开始执行连锁撤回操作');
            await this.performChainUndoOperations(entry.fileOperations, chainAnalysis);
            console.log('连锁撤回操作完成');
            // 清理工作流创建的文件夹
            if (entry.createdDirectories && entry.createdDirectories.length > 0) {
                console.log('开始清理工作流创建的文件夹');
                await this.cleanupCreatedDirectories(entry.createdDirectories);
                console.log('文件夹清理完成');
            }
            // 恢复被清理的空文件夹
            if (entry.cleanedEmptyDirectories && entry.cleanedEmptyDirectories.length > 0) {
                console.log('开始恢复被清理的空文件夹');
                await this.restoreCleanedEmptyDirectories(entry.cleanedEmptyDirectories);
                console.log('空文件夹恢复完成');
            }
            // 事务性更新历史记录状态
            await this.updateHistoryEntryStatus(entryId, {
                isUndone: true,
                undoTimestamp: new Date().toISOString(),
                canUndo: false
            });
            return { success: true, message: '连锁撤回操作成功完成' };
        }
        catch (error) {
            console.error('连锁撤回操作失败:', error);
            let errorMessage = '连锁撤回操作失败';
            if (error instanceof Error) {
                errorMessage = `连锁撤回失败：${error.message}`;
            }
            return { success: false, message: errorMessage };
        }
    }
    /**
     * 重做已撤销的操作
     */
    async redoEntry(entryId) {
        try {
            const history = await loadHistory();
            const entryIndex = history.findIndex((entry) => entry.id === entryId);
            if (entryIndex === -1) {
                return { success: false, message: '历史记录不存在' };
            }
            const entry = history[entryIndex];
            // 检查是否可以重做
            if (!entry.isUndone) {
                return { success: false, message: '此操作无法重做' };
            }
            // 执行重做操作
            await this.performRedoOperations(entry.fileOperations);
            // 更新原历史记录状态（原地更新，不创建新记录）
            history[entryIndex] = {
                ...entry,
                isUndone: false,
                canUndo: true,
                undoTimestamp: undefined
            };
            await saveHistory(history);
            // 清除内存缓存，确保前端获取到最新状态
            this.clearMemoryCache();
            return { success: true };
        }
        catch (error) {
            console.error('重做操作失败:', error);
            return { success: false, message: `重做操作失败: ${error instanceof Error ? error.message : String(error)}` };
        }
    }
    /**
     * 执行连锁撤回操作
     */
    async performChainUndoOperations(operations, chainAnalysis) {
        const errors = [];
        const warnings = [];
        console.log(`开始执行连锁撤回，共 ${chainAnalysis.executionOrder.length} 个操作`);
        console.log('执行顺序:', chainAnalysis.executionOrder.map(op => `${op.originalName} (${op.originalPath} -> ${op.newPath})`));
        // 使用临时路径避免冲突
        const tempMappings = new Map();
        // 第一阶段：将所有冲突的文件移动到临时位置
        for (const operation of chainAnalysis.executionOrder) {
            if (!operation.newPath || !operation.originalPath)
                continue;
            try {
                // 检查是否存在冲突
                const hasConflict = chainAnalysis.conflicts.some(c => c.operation.id === operation.id);
                if (hasConflict && await fs_extra_1.default.pathExists(operation.originalPath)) {
                    // 创建临时路径
                    const tempName = `chain-undo-temp-${operation.id}-${Date.now()}`;
                    const tempPath = require('path').join(require('path').dirname(operation.originalPath), tempName);
                    // 将占用目标位置的文件移动到临时位置
                    console.log(`🔄 临时移动冲突文件: ${operation.originalPath} -> ${tempPath}`);
                    await fs_extra_1.default.move(operation.originalPath, tempPath);
                    tempMappings.set(operation.originalPath, tempPath);
                }
            }
            catch (error) {
                const errorMsg = `临时移动文件失败 ${operation.originalPath}: ${error instanceof Error ? error.message : String(error)}`;
                console.error(errorMsg);
                errors.push(errorMsg);
            }
        }
        // 第二阶段：执行实际的撤回操作
        for (const operation of chainAnalysis.executionOrder) {
            try {
                switch (operation.operation) {
                    case 'move':
                    case 'rename':
                        await this.undoMoveOrRename(operation, errors, warnings);
                        break;
                    case 'copy':
                        await this.undoCopy(operation, errors, warnings);
                        break;
                    case 'delete':
                        warnings.push(`删除操作无法撤销，请从回收站手动恢复: ${operation.originalPath}`);
                        break;
                    case 'createFolder':
                        await this.undoCreateFolder(operation, errors, warnings);
                        break;
                }
            }
            catch (error) {
                const categorizedError = error instanceof Error
                    ? this.categorizeError(error, '连锁撤回', operation.originalPath)
                    : `连锁撤回操作失败 ${operation.originalPath}: ${String(error)}`;
                const suggestion = this.generateErrorSuggestion(categorizedError);
                const fullErrorMsg = `${categorizedError}\n${suggestion}`;
                console.error(fullErrorMsg);
                errors.push(fullErrorMsg);
            }
        }
        // 第三阶段：清理临时文件
        for (const [originalPath, tempPath] of tempMappings) {
            try {
                if (await fs_extra_1.default.pathExists(tempPath)) {
                    console.log(`🧹 清理临时文件: ${tempPath}`);
                    await fs_extra_1.default.remove(tempPath);
                }
            }
            catch (error) {
                console.warn(`清理临时文件失败 ${tempPath}:`, error);
            }
        }
        // 处理错误和警告
        if (errors.length > 0) {
            let errorMessage = `连锁撤回过程中发生错误:\n${errors.join('\n')}`;
            if (warnings.length > 0) {
                errorMessage += `\n\n警告:\n${warnings.join('\n')}`;
            }
            throw new Error(errorMessage);
        }
        else if (warnings.length > 0) {
            console.warn(`连锁撤回完成，但有警告:\n${warnings.join('\n')}`);
        }
    }
    /**
     * 执行撤销操作
     */
    async performUndoOperations(operations, operationId) {
        const errors = [];
        const warnings = [];
        // 只撤销成功的操作
        const successfulOperations = operations.filter(op => op.status === 'success');
        console.log(`开始撤销 ${successfulOperations.length} 个成功的操作（跳过 ${operations.length - successfulOperations.length} 个失败的操作）`);
        // 如果提供了操作ID，记录历史记录更新步骤用于回滚
        if (operationId) {
            this.logOperationStep(operationId, {
                id: `history-backup-${Date.now()}`,
                type: 'history_update',
                metadata: { operationType: 'undo_start' },
                timestamp: Date.now(),
                completed: false
            });
        }
        for (const operation of successfulOperations) {
            try {
                switch (operation.operation) {
                    case 'move':
                    case 'rename':
                        await this.undoMoveOrRename(operation, errors, warnings);
                        break;
                    case 'copy':
                        await this.undoCopy(operation, errors, warnings);
                        break;
                    case 'delete':
                        // 删除操作无法撤销，文件已移至回收站，用户可手动恢复
                        warnings.push(`删除操作无法撤销，请从回收站手动恢复: ${operation.originalPath}`);
                        break;
                    case 'createFolder':
                        await this.undoCreateFolder(operation, errors, warnings);
                        break;
                }
            }
            catch (error) {
                const categorizedError = error instanceof Error
                    ? this.categorizeError(error, '撤销', operation.originalPath)
                    : `撤销操作失败 ${operation.originalPath}: ${String(error)}`;
                const suggestion = this.generateErrorSuggestion(categorizedError);
                const fullErrorMsg = `${categorizedError}\n${suggestion}`;
                console.error(fullErrorMsg);
                errors.push(fullErrorMsg);
            }
        }
        // 处理错误和警告
        if (errors.length > 0) {
            let errorMessage = `撤销过程中发生错误:\n${errors.join('\n')}`;
            if (warnings.length > 0) {
                errorMessage += `\n\n警告:\n${warnings.join('\n')}`;
            }
            throw new Error(errorMessage);
        }
        else if (warnings.length > 0) {
            console.warn(`撤销完成，但有警告:\n${warnings.join('\n')}`);
        }
    }
    /**
     * 撤销移动或重命名操作 (终极修复版：采用“偏执”的两步法)
     */
    async undoMoveOrRename(operation, errors, warnings) {
        const sourcePath = operation.newPath;
        const finalDestPath = operation.originalPath;
        console.log(`[防御性撤销] 准备执行: ${sourcePath} -> ${finalDestPath}`);
        if (!sourcePath || !finalDestPath) {
            errors.push(`撤销失败：操作记录无效，源或目标路径缺失。`);
            return;
        }
        // 步骤 1: 检查源文件/文件夹是否存在。
        if (!await fs_extra_1.default.pathExists(sourcePath)) {
            errors.push(`撤销失败：源文件/文件夹已不存在于 ${sourcePath}`);
            return;
        }
        // 步骤 2: 再次确认最终目标位置是否已被占用。这是核心防御。
        if (await fs_extra_1.default.pathExists(finalDestPath)) {
            errors.push(`撤销失败：目标路径 ${finalDestPath} 已被占用，无法继续。`);
            return;
        }
        // 步骤 3: 确保目标位置的父目录存在。
        const destParentDir = require('path').dirname(finalDestPath);
        try {
            await fs_extra_1.default.ensureDir(destParentDir);
        }
        catch (err) {
            const errorMsg = err instanceof Error ? err.message : String(err);
            errors.push(`撤销失败：无法创建父目录 ${destParentDir}。错误: ${errorMsg}`);
            return;
        }
        // 步骤 4: 执行“偏执”的两步移动，彻底规避嵌套问题。
        // 创建一个唯一的临时路径，用于中转。
        const tempName = `undo-temp-${operation.id}-${Date.now()}`;
        const tempPath = require('path').join(destParentDir, tempName);
        try {
            // 第 A 步：将源移动到一个保证不存在的临时路径下。
            // 这个 move 操作绝不会有歧义。
            await fs_extra_1.default.move(sourcePath, tempPath);
            // 第 B 步：将临时文件/文件夹重命名为最终名称。
            // fs.rename 是一个更原子性的操作，如果目标已存在，它会直接失败而不是嵌套。
            await fs_extra_1.default.rename(tempPath, finalDestPath);
            console.log(`✅ 成功撤销: ${sourcePath} -> ${finalDestPath}`);
        }
        catch (err) {
            const errorMsg = `撤销失败：在移动/重命名过程中发生错误。目标: ${finalDestPath}. 错误: ${err instanceof Error ? err.message : String(err)}`;
            console.error(errorMsg, err);
            errors.push(errorMsg);
            // 如果发生错误，尝试清理残留的临时文件/文件夹。
            if (await fs_extra_1.default.pathExists(tempPath)) {
                await fs_extra_1.default.remove(tempPath).catch(cleanupErr => {
                    console.error(`!!! 清理临时文件中转失败: ${tempPath}`, cleanupErr);
                });
            }
        }
    }
    /**
     * 撤销创建文件夹操作
     */
    async undoCreateFolder(operation, errors, warnings) {
        const folderPath = operation.newPath || operation.originalPath;
        if (!folderPath) {
            errors.push(`操作记录不完整，缺少文件夹路径: ${operation.originalName}`);
            return;
        }
        if (!await fs_extra_1.default.pathExists(folderPath)) {
            warnings.push(`要删除的文件夹不存在（可能已被手动删除）: ${folderPath}`);
            return;
        }
        try {
            // 检查文件夹是否为空
            const items = await fs_extra_1.default.readdir(folderPath);
            if (items.length > 0) {
                warnings.push(`文件夹不为空，无法撤销创建操作: ${folderPath} (包含 ${items.length} 个项目)`);
                return;
            }
            // 删除空文件夹
            await fs_extra_1.default.rmdir(folderPath);
            console.log(`✅ 成功撤销文件夹创建，已删除: ${folderPath}`);
        }
        catch (removeError) {
            const errorMsg = `删除创建的文件夹失败: ${folderPath}`;
            console.error(errorMsg, removeError);
            errors.push(`${errorMsg}: ${removeError instanceof Error ? removeError.message : String(removeError)}`);
        }
    }
    /**
     * 撤销复制操作
     */
    async undoCopy(operation, errors, warnings) {
        if (!operation.newPath) {
            errors.push(`操作记录不完整，缺少新路径: ${operation.originalPath}`);
            return;
        }
        if (!await fs_extra_1.default.pathExists(operation.newPath)) {
            warnings.push(`要删除的复制文件/文件夹不存在（可能已被手动删除）: ${operation.newPath}`);
            return;
        }
        try {
            // 获取文件/文件夹信息以确定类型
            const stat = await fs_extra_1.default.stat(operation.newPath);
            const isDirectory = stat.isDirectory();
            // 删除复制的文件/文件夹
            await fs_extra_1.default.remove(operation.newPath);
            console.log(`✅ 成功撤销${isDirectory ? '文件夹' : '文件'}复制，已删除: ${operation.newPath}`);
        }
        catch (removeError) {
            const errorMsg = `删除复制的文件/文件夹失败: ${operation.newPath}`;
            console.error(errorMsg, removeError);
            errors.push(`${errorMsg}: ${removeError instanceof Error ? removeError.message : String(removeError)}`);
        }
    }
    /**
     * 预检查撤销操作的可行性 (支持连锁重命名检测)
     */
    async preCheckUndoOperations(operations) {
        const issues = [];
        const successfulOperations = operations.filter(op => op.status === 'success');
        // 检测连锁重命名冲突
        const chainConflicts = await this.detectChainRenameConflicts(successfulOperations);
        if (chainConflicts.length > 0) {
            issues.push(...chainConflicts);
        }
        // 检查磁盘空间
        const requiredSpace = this.calculateRequiredSpace(successfulOperations);
        if (requiredSpace > 0) {
            // 检查主要目标路径的磁盘空间
            const moveOperations = successfulOperations.filter(op => (op.operation === 'move' || op.operation === 'rename') && op.originalPath);
            for (const operation of moveOperations.slice(0, 3)) { // 只检查前3个，避免过多检查
                const spaceCheck = await this.checkDiskSpace(operation.originalPath, requiredSpace);
                if (!spaceCheck.hasSpace) {
                    issues.push(`[预检警告] ${spaceCheck.error}`);
                    break; // 只报告一次磁盘空间问题
                }
            }
        }
        for (const operation of successfulOperations) {
            const sourcePath = operation.newPath;
            const finalDestPath = operation.originalPath;
            try {
                switch (operation.operation) {
                    case 'move':
                    case 'rename':
                        if (!sourcePath || !finalDestPath) {
                            issues.push(`[预检失败] 操作记录无效: ${operation.originalName}`);
                            continue;
                        }
                        if (!await fs_extra_1.default.pathExists(sourcePath)) {
                            issues.push(`[预检失败] 源文件/夹不存在: ${sourcePath}`);
                            continue;
                        }
                        // 对于连锁重命名，我们在上面已经检测过了，这里跳过简单的存在性检查
                        if (await fs_extra_1.default.pathExists(finalDestPath) && chainConflicts.length === 0) {
                            issues.push(`[预检失败] 目标位置已被占用: ${finalDestPath}`);
                            continue;
                        }
                        // 检查源文件的读取权限
                        const sourcePermCheck = await this.checkPermissions(sourcePath, 'read');
                        if (!sourcePermCheck.hasPermission) {
                            issues.push(`[预检失败] 源文件权限不足: ${sourcePath} - ${sourcePermCheck.error}`);
                            continue;
                        }
                        // 检查目标位置的写入权限
                        const targetDir = require('path').dirname(finalDestPath);
                        const targetPermCheck = await this.checkDirectoryPermissions(targetDir);
                        if (!targetPermCheck.hasPermission) {
                            issues.push(`[预检失败] 目标目录权限不足: ${targetDir} - ${targetPermCheck.error}`);
                            continue;
                        }
                        break;
                    case 'copy':
                        if (!sourcePath) {
                            issues.push(`[预检失败] 复制记录无效: ${operation.originalName}`);
                            continue;
                        }
                        // 对于复制操作，只需要检查复制的文件是否存在（不存在也不是问题，只是警告）
                        if (await fs_extra_1.default.pathExists(sourcePath)) {
                            // 检查是否有删除权限
                            const deletePermCheck = await this.checkPermissions(sourcePath, 'write');
                            if (!deletePermCheck.hasPermission) {
                                issues.push(`[预检失败] 复制的文件/夹不可删除: ${sourcePath} - ${deletePermCheck.error}`);
                            }
                        }
                        break;
                    case 'delete':
                        // 删除操作无法撤销，但不算作阻止撤销的问题
                        break;
                    case 'createFolder':
                        const folderPath = operation.newPath || operation.originalPath;
                        if (!folderPath) {
                            issues.push(`[预检失败] 创建文件夹记录无效: ${operation.originalName}`);
                            continue;
                        }
                        // 检查创建的文件夹是否存在（不存在也不是问题，只是警告）
                        if (await fs_extra_1.default.pathExists(folderPath)) {
                            // 检查文件夹是否为空
                            try {
                                const items = await fs_extra_1.default.readdir(folderPath);
                                if (items.length > 0) {
                                    issues.push(`[预检警告] 创建的文件夹不为空，无法撤销: ${folderPath}`);
                                }
                            }
                            catch {
                                issues.push(`[预检失败] 无法读取创建的文件夹: ${folderPath}`);
                            }
                        }
                        break;
                }
            }
            catch (error) {
                issues.push(`检查操作 ${operation.originalName} 时出错: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        return {
            canUndo: issues.length === 0,
            issues
        };
    }
    /**
     * 检测连锁重命名冲突
     */
    async detectChainRenameConflicts(operations) {
        const conflicts = [];
        const renameOps = operations.filter(op => op.operation === 'rename' || op.operation === 'move');
        if (renameOps.length === 0) {
            return conflicts;
        }
        console.log(`🔍 检测连锁重命名冲突，共 ${renameOps.length} 个重命名操作`);
        // 构建撤回目标路径映射
        const undoTargets = new Map();
        for (const op of renameOps) {
            if (op.originalPath) {
                undoTargets.set(op.originalPath, op);
            }
        }
        // 检查每个操作的撤回目标是否被其他文件占用
        for (const operation of renameOps) {
            const sourcePath = operation.newPath;
            const targetPath = operation.originalPath;
            if (!sourcePath || !targetPath)
                continue;
            // 检查目标位置是否存在文件
            if (await fs_extra_1.default.pathExists(targetPath)) {
                // 检查占用该位置的文件是否也是本次工作流的产物
                const occupyingOp = renameOps.find(op => op.newPath === targetPath);
                if (occupyingOp) {
                    // 这是一个连锁重命名冲突
                    console.log(`⚠️ 检测到连锁重命名冲突:`);
                    console.log(`  - 操作1: ${occupyingOp.originalPath} -> ${occupyingOp.newPath}`);
                    console.log(`  - 操作2: ${operation.originalPath} -> ${operation.newPath}`);
                    console.log(`  - 冲突: 操作2想要撤回到 ${targetPath}，但该位置被操作1的结果占用`);
                    conflicts.push(`[连锁冲突] 无法撤回 ${operation.originalName}，因为目标位置 ${targetPath} 被同批次操作的文件占用。建议使用连锁撤回功能。`);
                }
                else {
                    // 被其他文件占用（非本次工作流产物）
                    conflicts.push(`[预检失败] 目标位置已被其他文件占用: ${targetPath}`);
                }
            }
        }
        return conflicts;
    }
    /**
     * 分析连锁依赖关系
     */
    async analyzeChainDependencies(operations) {
        const conflicts = [];
        const renameOps = operations.filter(op => (op.operation === 'rename' || op.operation === 'move') && op.status === 'success');
        // 构建依赖图
        for (const operation of renameOps) {
            const targetPath = operation.originalPath;
            if (!targetPath)
                continue;
            // 查找占用目标位置的操作
            const blockingOp = renameOps.find(op => op.newPath === targetPath);
            if (blockingOp && blockingOp !== operation) {
                conflicts.push({ operation, blockingOperation: blockingOp });
            }
        }
        // 计算执行顺序（拓扑排序）
        const executionOrder = this.calculateUndoOrder(renameOps, conflicts);
        return { conflicts, executionOrder };
    }
    /**
     * 计算撤回执行顺序
     */
    calculateUndoOrder(operations, conflicts) {
        const order = [];
        const visited = new Set();
        const visiting = new Set();
        // 构建依赖图
        const dependencies = new Map();
        for (const op of operations) {
            dependencies.set(op.id, []);
        }
        for (const conflict of conflicts) {
            const deps = dependencies.get(conflict.operation.id) || [];
            deps.push(conflict.blockingOperation.id);
            dependencies.set(conflict.operation.id, deps);
        }
        // 深度优先搜索进行拓扑排序
        const dfs = (opId) => {
            if (visiting.has(opId)) {
                // 检测到循环依赖
                console.warn(`检测到循环依赖: ${opId}`);
                return false;
            }
            if (visited.has(opId)) {
                return true;
            }
            visiting.add(opId);
            const deps = dependencies.get(opId) || [];
            for (const depId of deps) {
                if (!dfs(depId)) {
                    return false;
                }
            }
            visiting.delete(opId);
            visited.add(opId);
            const operation = operations.find(op => op.id === opId);
            if (operation) {
                order.unshift(operation); // 添加到开头，因为依赖的操作需要先执行
            }
            return true;
        };
        // 对所有操作进行排序
        for (const operation of operations) {
            if (!visited.has(operation.id)) {
                dfs(operation.id);
            }
        }
        return order;
    }
    /**
     * 执行重做操作 (终极修复版：采用“偏执”的两步法)
     */
    async performRedoOperations(operations) {
        const errors = [];
        const warnings = [];
        // 只重做成功的操作
        const successfulOperations = operations.filter(op => op.status === 'success');
        for (const operation of successfulOperations) {
            const sourcePath = operation.originalPath; // 重做时，源是原始路径
            const finalDestPath = operation.newPath; // 目标是新路径
            let tempPath; // 声明临时路径变量
            try {
                switch (operation.operation) {
                    case 'move':
                    case 'rename':
                        console.log(`[防御性重做] 准备执行: ${sourcePath} -> ${finalDestPath}`);
                        if (!sourcePath || !finalDestPath) {
                            errors.push(`重做失败：操作记录无效，路径缺失。`);
                            continue;
                        }
                        if (!await fs_extra_1.default.pathExists(sourcePath)) {
                            errors.push(`重做失败：源文件/夹不存在: ${sourcePath}`);
                            continue;
                        }
                        if (await fs_extra_1.default.pathExists(finalDestPath)) {
                            errors.push(`重做失败：目标位置已被占用: ${finalDestPath}`);
                            continue;
                        }
                        const destParentDir = require('path').dirname(finalDestPath);
                        await fs_extra_1.default.ensureDir(destParentDir);
                        // 采用与撤销完全相同的“偏执”两步法
                        const tempName = `redo-temp-${operation.id}-${Date.now()}`;
                        tempPath = require('path').join(destParentDir, tempName);
                        await fs_extra_1.default.move(sourcePath, tempPath);
                        await fs_extra_1.default.rename(tempPath, finalDestPath);
                        console.log(`✅ 成功重做: ${sourcePath} -> ${finalDestPath}`);
                        break;
                    case 'copy':
                        if (finalDestPath) {
                            await fs_extra_1.default.copy(sourcePath, finalDestPath);
                            console.log(`✅ 成功重做复制: ${sourcePath} -> ${finalDestPath}`);
                        }
                        break;
                    case 'delete':
                        // 理论上重做“删除”需要将文件移到回收站，这是一个复杂且依赖平台的功能。
                        // 目前简单地标记为无法重做是合理的。
                        warnings.push(`删除操作无法自动重做: ${sourcePath}`);
                        break;
                }
            }
            catch (err) {
                const errorMsg = `重做操作失败 ${operation.originalPath}: ${err instanceof Error ? err.message : String(err)}`;
                console.error(errorMsg, err);
                errors.push(errorMsg);
                // 如果发生错误，尝试清理残留的临时文件/文件夹。
                if (tempPath && await fs_extra_1.default.pathExists(tempPath)) {
                    await fs_extra_1.default.remove(tempPath).catch(cleanupErr => {
                        console.error(`!!! 清理临时文件中转失败: ${tempPath}`, cleanupErr);
                    });
                }
            }
        }
        if (errors.length > 0) {
            let errorMessage = `重做过程中发生错误:\n${errors.join('\n')}`;
            if (warnings.length > 0) {
                errorMessage += `\n\n警告:\n${warnings.join('\n')}`;
            }
            throw new Error(errorMessage);
        }
        else if (warnings.length > 0) {
            console.warn(`重做完成，但有警告:\n${warnings.join('\n')}`);
        }
    }
    /**
     * 清理工作流执行过程中创建的文件夹
     */
    async cleanupCreatedDirectories(createdDirectories) {
        if (!createdDirectories || createdDirectories.length === 0) {
            console.log('没有需要清理的工作流创建文件夹');
            return;
        }
        console.log(`开始清理 ${createdDirectories.length} 个工作流创建的文件夹...`);
        console.log('待清理的文件夹列表:', createdDirectories);
        // 按路径深度排序，从最深的开始清理
        const sortedDirs = [...createdDirectories].sort((a, b) => {
            const depthA = a.split(require('path').sep).length;
            const depthB = b.split(require('path').sep).length;
            return depthB - depthA; // 深度大的在前
        });
        let cleanedCount = 0;
        let skippedCount = 0;
        const errors = [];
        for (const dirPath of sortedDirs) {
            try {
                // 检查目录是否存在
                if (!await fs_extra_1.default.pathExists(dirPath)) {
                    console.log(`📂 文件夹已不存在，跳过: ${dirPath}`);
                    continue;
                }
                // 检查目录是否为空
                const items = await fs_extra_1.default.readdir(dirPath);
                if (items.length === 0) {
                    await fs_extra_1.default.rmdir(dirPath);
                    cleanedCount++;
                    console.log(`✅ 已清理空文件夹: ${dirPath}`);
                }
                else {
                    skippedCount++;
                    console.log(`⚠️ 文件夹不为空，跳过清理: ${dirPath} (包含 ${items.length} 个项目: ${items.slice(0, 3).join(', ')}${items.length > 3 ? '...' : ''})`);
                    // 对于非空文件夹，检查是否只包含我们创建的子文件夹
                    const onlyContainsCreatedDirs = items.every(item => {
                        const itemPath = require('path').join(dirPath, item);
                        return createdDirectories.includes(itemPath);
                    });
                    if (onlyContainsCreatedDirs) {
                        console.log(`🔍 文件夹 ${dirPath} 只包含工作流创建的子文件夹，将在子文件夹清理后重新检查`);
                    }
                }
            }
            catch (error) {
                const errorMsg = `清理文件夹失败 ${dirPath}: ${error instanceof Error ? error.message : String(error)}`;
                console.warn(errorMsg);
                errors.push(errorMsg);
            }
        }
        // 第二轮清理：清理在第一轮中变为空的文件夹
        console.log(`🔄 开始第二轮清理，检查是否有文件夹在第一轮清理后变为空...`);
        let secondRoundCleaned = 0;
        for (const dirPath of sortedDirs) {
            try {
                // 检查目录是否存在且为空
                if (await fs_extra_1.default.pathExists(dirPath)) {
                    const items = await fs_extra_1.default.readdir(dirPath);
                    if (items.length === 0) {
                        await fs_extra_1.default.rmdir(dirPath);
                        secondRoundCleaned++;
                        cleanedCount++;
                        console.log(`✅ 第二轮清理空文件夹: ${dirPath}`);
                    }
                }
            }
            catch (error) {
                const errorMsg = `第二轮清理文件夹失败 ${dirPath}: ${error instanceof Error ? error.message : String(error)}`;
                console.warn(errorMsg);
                errors.push(errorMsg);
            }
        }
        if (secondRoundCleaned > 0) {
            console.log(`🎯 第二轮清理了 ${secondRoundCleaned} 个文件夹`);
        }
        // 详细的清理结果报告
        console.log(`📊 文件夹清理完成:`);
        console.log(`  - 成功清理: ${cleanedCount} 个空文件夹`);
        console.log(`  - 跳过清理: ${skippedCount} 个非空文件夹`);
        console.log(`  - 清理错误: ${errors.length} 个`);
        if (cleanedCount > 0) {
            console.log(`✅ 成功清理了 ${cleanedCount} 个工作流创建的空文件夹`);
        }
        else {
            console.log(`ℹ️ 没有发现需要清理的空文件夹`);
        }
        if (errors.length > 0) {
            console.warn(`清理过程中发生 ${errors.length} 个错误，但不影响撤销操作:`);
            errors.forEach(error => console.warn(`  - ${error}`));
        }
    }
    /**
     * 恢复被清理的空文件夹
     */
    async restoreCleanedEmptyDirectories(cleanedEmptyDirectories) {
        if (!cleanedEmptyDirectories || cleanedEmptyDirectories.length === 0) {
            console.log('没有需要恢复的空文件夹');
            return;
        }
        console.log(`开始恢复 ${cleanedEmptyDirectories.length} 个被清理的空文件夹...`);
        console.log('待恢复的文件夹列表:', cleanedEmptyDirectories);
        // 按路径深度排序，从最浅的开始恢复（与清理顺序相反）
        const sortedDirs = [...cleanedEmptyDirectories].sort((a, b) => {
            const depthA = a.split(require('path').sep).length;
            const depthB = b.split(require('path').sep).length;
            return depthA - depthB; // 深度小的在前
        });
        let restoredCount = 0;
        let skippedCount = 0;
        const errors = [];
        for (const dirPath of sortedDirs) {
            try {
                // 检查目录是否已经存在
                if (await fs_extra_1.default.pathExists(dirPath)) {
                    console.log(`📂 文件夹已存在，跳过恢复: ${dirPath}`);
                    skippedCount++;
                    continue;
                }
                // 创建空文件夹
                await fs_extra_1.default.ensureDir(dirPath);
                restoredCount++;
                console.log(`✅ 已恢复空文件夹: ${dirPath}`);
            }
            catch (error) {
                const errorMsg = `恢复空文件夹失败 ${dirPath}: ${error instanceof Error ? error.message : String(error)}`;
                console.warn(errorMsg);
                errors.push(errorMsg);
            }
        }
        // 详细的恢复结果报告
        console.log(`📊 空文件夹恢复完成:`);
        console.log(`  - 成功恢复: ${restoredCount} 个空文件夹`);
        console.log(`  - 跳过恢复: ${skippedCount} 个已存在的文件夹`);
        console.log(`  - 恢复错误: ${errors.length} 个`);
        if (restoredCount > 0) {
            console.log(`✅ 成功恢复了 ${restoredCount} 个被清理的空文件夹`);
        }
        else {
            console.log(`ℹ️ 没有需要恢复的空文件夹或文件夹已存在`);
        }
        if (errors.length > 0) {
            console.warn(`恢复过程中发生 ${errors.length} 个错误，但不影响撤销操作:`);
            errors.forEach(error => console.warn(`  - ${error}`));
        }
    }
}
exports.HistoryManager = HistoryManager;
// 导出单例实例
const historyManager = new HistoryManager();
module.exports = { HistoryManager, historyManager };
