# 条件编辑器字段选择框宽度优化

## 问题描述
在英文模式下，文件夹筛选条件中的字段选择框因为宽度太窄，导致长文本（如"Subfolder Count"）显示时换行，影响UI美观。

## 问题分析

### 原始问题
- 字段选择框固定宽度：`w-36` (144px)
- 英文长文本无法在一行内完整显示
- 特别是"Subfolder Count"等长字段名

### 英文字段名长度分析
**优化前的长文本：**
- "File Extension" (14字符)
- "Created Date" (12字符)  
- "Modified Date" (13字符)
- "File Count" (10字符)
- "Subfolder Count" (15字符) ← 最长

## 解决方案

### 1. 翻译文本优化
简化英文翻译，保持意思清晰的同时减少字符数：

```diff
- 'condition.field.fileExtension': 'File Extension'
+ 'condition.field.fileExtension': 'Extension'

- 'condition.field.createdDate': 'Created Date'
+ 'condition.field.createdDate': 'Created'

- 'condition.field.modifiedDate': 'Modified Date'  
+ 'condition.field.modifiedDate': 'Modified'

- 'condition.field.folderFileCount': 'File Count'
+ 'condition.field.folderFileCount': 'Files'

- 'condition.field.folderSubfolderCount': 'Subfolder Count'
+ 'condition.field.folderSubfolderCount': 'Subfolders'
```

### 2. 布局宽度优化
使用响应式宽度替代固定宽度：

```diff
- <div className="w-36 flex-shrink-0 relative">
+ <div className="min-w-[140px] max-w-[180px] flex-shrink-0 relative">
```

**优化说明：**
- `min-w-[140px]`：确保中文短文本也有足够显示空间
- `max-w-[180px]`：为英文长文本提供更多空间
- `flex-shrink-0`：保持不收缩特性
- 移除固定宽度，允许根据内容自适应

## 优化效果

### 文本长度对比
| 字段 | 优化前 | 优化后 | 节省字符 |
|------|--------|--------|----------|
| 文件扩展名 | File Extension (14) | Extension (9) | 5 |
| 创建日期 | Created Date (12) | Created (7) | 5 |
| 修改日期 | Modified Date (13) | Modified (8) | 5 |
| 文件数量 | File Count (10) | Files (5) | 5 |
| 子文件夹数量 | Subfolder Count (15) | Subfolders (10) | 5 |

### 布局改进
- ✅ 解决了英文模式下文本换行问题
- ✅ 保持了中文模式下的正常显示
- ✅ 提供了更灵活的宽度适应性
- ✅ 维持了整体布局的协调性

## 技术实现

### 修改文件
1. `src/renderer/contexts/language-context.tsx` - 优化英文翻译
2. `src/renderer/components/condition-editor.tsx` - 调整布局宽度

### CSS类变更
```css
/* 原始 */
.w-36 { width: 9rem; /* 144px */ }

/* 优化后 */
.min-w-[140px] { min-width: 140px; }
.max-w-[180px] { max-width: 180px; }
```

## 测试验证
- ✅ 编译无错误
- ✅ 中文模式显示正常
- ✅ 英文模式文本不再换行
- ✅ 响应式宽度工作正常
- ✅ 整体布局保持协调

这次优化通过双重策略（文本简化 + 布局优化）彻底解决了英文模式下字段选择框的显示问题。
